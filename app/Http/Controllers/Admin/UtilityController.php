<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Utility;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class UtilityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $utilities = Utility::withCount('fields')
            ->orderBy('name')
            ->get();

        return view('admin.utilities.index', compact('utilities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('admin.utilities.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:utilities,name',
            'description' => 'nullable|string|max:1000',
            'hourly_rate' => 'nullable|numeric|min:0|max:999999.99',
            'icon_class' => 'required|string|max:255',
        ]);

        // Set default active status for new utilities
        $validated['is_active'] = true;

        $utility = Utility::create($validated);

        return redirect()
            ->route('admin.utilities.index')
            ->with('success', 'Utility created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Utility $utility): View
    {
        $utility->load('fields');

        return view('admin.utilities.show', compact('utility'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Utility $utility): View
    {
        return view('admin.utilities.edit', compact('utility'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Utility $utility): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:utilities,name,'.$utility->id,
            'description' => 'nullable|string|max:1000',
            'hourly_rate' => 'nullable|numeric|min:0|max:999999.99',
            'icon_class' => 'required|string|max:255',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->has('is_active');

        $utility->update($validated);

        return redirect()
            ->route('admin.utilities.show', $utility)
            ->with('success', 'Utility updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Utility $utility): RedirectResponse
    {
        // Check if utility is being used by any fields
        if ($utility->fields()->count() > 0) {
            return redirect()
                ->route('admin.utilities.index')
                ->with('error', 'Cannot delete utility that is being used by fields. Please remove it from all fields first.');
        }

        $utility->delete();

        return redirect()
            ->route('admin.utilities.index')
            ->with('success', 'Utility deleted successfully.');
    }

    /**
     * Bulk actions for utilities.
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'utilities' => 'required|array',
            'utilities.*' => 'exists:utilities,id',
        ]);

        $utilities = Utility::whereIn('id', $request->utilities);

        switch ($request->action) {
            case 'activate':
                $utilities->update(['is_active' => true]);
                $message = 'Selected utilities have been activated.';
                break;
            case 'deactivate':
                $utilities->update(['is_active' => false]);
                $message = 'Selected utilities have been deactivated.';
                break;
            case 'delete':
                // Check if any of the utilities are being used
                $usedUtilities = $utilities->withCount('fields')->get()->filter(function ($utility) {
                    return $utility->fields_count > 0;
                });

                if ($usedUtilities->count() > 0) {
                    return redirect()
                        ->route('admin.utilities.index')
                        ->with('error', 'Cannot delete utilities that are being used by fields.');
                }

                $utilities->delete();
                $message = 'Selected utilities have been deleted.';
                break;
        }

        return redirect()
            ->route('admin.utilities.index')
            ->with('success', $message);
    }
}
